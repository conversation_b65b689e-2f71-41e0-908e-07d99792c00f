"""added refunds table

Revision ID: 8460c42c4c8b
Revises: 4b6a7c487108
Create Date: 2025-08-29 01:41:28.196059

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8460c42c4c8b'
down_revision: Union[str, Sequence[str], None] = '4b6a7c487108'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('refund_details',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('payment_id', sa.Integer(), nullable=False),
    sa.Column('refund_id', sa.String(length=50), nullable=False),
    sa.Column('refund_amount', sa.DECIMAL(precision=10, scale=2), nullable=False),
    sa.Column('refund_currency', sa.String(length=6), server_default='INR', nullable=False),
    sa.Column('refund_status', sa.Integer(), server_default='60', nullable=False),
    sa.Column('refund_date', sa.TIMESTAMP(), nullable=True),
    sa.Column('speed_requested', sa.String(length=20), nullable=True),
    sa.Column('speed_processed', sa.String(length=20), nullable=True),
    sa.Column('receipt', sa.String(length=100), nullable=True),
    sa.Column('batch_id', sa.String(length=50), nullable=True),
    sa.Column('acquirer_data', sa.String(length=500), nullable=True),
    sa.Column('notes', sa.String(length=1000), nullable=True),
    sa.Column('created_at', sa.TIMESTAMP(timezone=True), nullable=False),
    sa.Column('updated_at', sa.TIMESTAMP(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['payment_id'], ['payment_details.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_refund_details_payment_id_status', 'refund_details', ['payment_id', 'refund_status'], unique=False)
    op.create_index('idx_refund_details_status_created', 'refund_details', ['refund_status', 'created_at'], unique=False)
    op.create_index(op.f('ix_refund_details_id'), 'refund_details', ['id'], unique=False)
    op.create_index(op.f('ix_refund_details_payment_id'), 'refund_details', ['payment_id'], unique=False)
    op.create_index(op.f('ix_refund_details_refund_id'), 'refund_details', ['refund_id'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_refund_details_refund_id'), table_name='refund_details')
    op.drop_index(op.f('ix_refund_details_payment_id'), table_name='refund_details')
    op.drop_index(op.f('ix_refund_details_id'), table_name='refund_details')
    op.drop_index('idx_refund_details_status_created', table_name='refund_details')
    op.drop_index('idx_refund_details_payment_id_status', table_name='refund_details')
    op.drop_table('refund_details')
    # ### end Alembic commands ###
