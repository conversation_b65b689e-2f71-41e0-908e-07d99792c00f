from fastapi import APIR<PERSON><PERSON>, Query, Header
from typing import Optional
from pydantic import BaseModel

from app.core.token_validation_core import (
    validate_token_and_get_orders,
    validate_token_and_get_order_items,
    validate_token_and_cancel_order,
)
from app.core.order_functions import get_order_details_core

api_router = APIRouter(tags=["api"])

class CancelOrderRequest(BaseModel):
    customer_id: str
    order_id: str

@api_router.get("/get_orders")
async def get_orders(
    customer_id: str = Query(..., description="Customer ID"),
    page_size: int = Query(20, description="Number of orders to return per page"),
    page: int = Query(1, description="Page number (starting from 1)"),
    sort_order: str = Query("desc", description="Sort order (asc/desc)"),
    authorization: str = Header(..., description="Token for authentication")
):
    return await validate_token_and_get_orders(
        token=authorization,
        customer_id=customer_id,
        page_size=page_size,
        page=page,
        sort_order=sort_order
    )

@api_router.get("/order_details")
async def get_order_details(order_id: str = Query(..., description="Order ID")):
    return await get_order_details_core(order_id)

@api_router.get("/order_items")
async def get_order_items(
    customer_id: str = Query(..., description="Customer ID"),
    order_id: str = Query(..., description="Order ID to get items for"),
    authorization: str = Header(..., description="Token for authentication")
):
    return await validate_token_and_get_order_items(
        token=authorization,
        customer_id=customer_id,
        order_id=order_id
    )

@api_router.post("/cancel_order")
async def cancel_order(
    cancel_request: CancelOrderRequest,
    authorization: str = Header(..., description="Token for authentication")
):
    return await validate_token_and_cancel_order(
        token=authorization,
        customer_id=cancel_request.customer_id,
        order_id=cancel_request.order_id
    )
