"""
External Wallet Service for handling wallet operations.

This service communicates with an external wallet API to handle
wallet balance checks, debit operations, and transaction confirmations.
"""

import httpx
from typing import Dict, Any, Optional
from decimal import Decimal
import uuid
from pydantic import BaseModel

# Logger
from app.logging.utils import get_app_logger
logger = get_app_logger("wallet_service")

# Settings
from app.config.settings import OMSConfigs
configs = OMSConfigs()

class WalletServiceReturnMessage(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None


class WalletService:
    """Service for communicating with external wallet API"""

    def __init__(self):
        self.wallet_enabled = configs.WALLET_INTEGRATION_ENABLED
        self.wallet_api_url = configs.WALLET_BASE_URL
        self.wallet_api_key = configs.WALLET_INTERNAL_API_KEY
        self.timeout = 30  # seconds

        if not self.wallet_enabled or not self.wallet_api_url or not self.wallet_api_key:
            raise ValueError("Wallet integration is disabled or not configured")

    def _get_headers(self) -> Dict[str, str]:
        """Get headers for wallet API requests"""
        return {
            "Content-Type": "application/json",
            "User-Agent": "Rozana-OMS/1.0",
            "X-API-Key": self.wallet_api_key
        }

    def return_message(self, success: bool, message: str, data: Optional[Dict[str, Any]] = None) -> WalletServiceReturnMessage:
        return WalletServiceReturnMessage(success=success, message=message, data=data)

    async def check_balance(self, customer_id: str) -> WalletServiceReturnMessage:
        """
        Check wallet balance for a customer.
        Args:
            customer_id: Customer ID
        Returns:
            Dict with balance information
        """
        self.url = f"{self.wallet_api_url}/balance/{customer_id}"
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(self.url, headers=self._get_headers())
                
                if response.status_code == 200:
                    data = response.json()
                    return self.return_message(success=True,
                        message="Wallet balance checked successfully",
                        data={
                            "balance": float(data.get("balance", 0.0)),
                            "currency": data.get("currency", "INR"),
                            "is_active": data.get("is_active", True)
                        }
                    )
                elif response.status_code == 404:
                    return self.return_message(success=False, message="Wallet not found", data={"balance": 0.0})
                else:
                    logger.error(f"Balance check failed: {response.status_code} - {response.text}")
                    return self.return_message(success=False, message=f"Balance check failed: {response.status_code}", data={"balance": 0.0})
        except httpx.TimeoutException:
            logger.error("Wallet service timeout during balance check")
            return self.return_message(success=False, message="Wallet service timeout", data={"balance": 0.0})
        except Exception as e:
            logger.error(f"Error checking wallet balance: {e}")
            return self.return_message(success=False, message=f"Balance check error: {str(e)}", data={"balance": 0.0})

    async def add_wallet_entry(self, customer_id: str, amount: Decimal, order_id: str, payment_id: str, entry_type: str = "debit", reference_type: str = "order_payment", description: str = None) -> WalletServiceReturnMessage:
        """
        Add wallet entry (debit/credit) using the actual wallet API format.
        Args:
            customer_id: Customer ID
            amount: Amount for the entry
            order_id: Order ID for reference
            payment_id: Payment ID for reference
            entry_type: "debit" or "credit"
            reference_type: Type of reference (e.g., "order_payment")
            description: Optional description
        Returns:
            Dict with wallet entry result
        """
        self.url = f"{self.wallet_api_url}/internal/wallet-entry/{customer_id}/add-entry"
        try:
            related_id = f"oms_{payment_id}_{uuid.uuid4().hex[:8]}"
            payload = {
                "related_id": related_id,
                "wallet_amt": float(amount),
                "entry_type": entry_type,
                "description": description or f"Payment for order {order_id}",
                "reference_type": reference_type
            }

            # Use the actual API endpoint format
            headers = self._get_headers()
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(self.url, headers=headers, json=payload)
                
                if response.status_code == 200:
                    data = response.json()
                    return self.return_message(success=True, message="Wallet entry added successfully",
                        data={
                            "transaction_id": related_id,
                            "amount": float(amount),
                            "balance_after": data.get("balance_after", 0.0),
                            "status": data.get("status", "completed")
                        }
                    )
                elif response.status_code == 400:
                    data = response.json()
                    return self.return_message(success=False, message=data.get("message", "Insufficient balance"), data={"transaction_id": None})
                else:
                    logger.error(f"Wallet entry failed: {response.status_code} - {response.text}")
                    return self.return_message(success=False, message=f"Wallet entry failed: {response.status_code}", data={"transaction_id": None})
        except httpx.TimeoutException:
            logger.error("Wallet service timeout during wallet entry")
            return self.return_message(success=False, message="Wallet service timeout", data={"transaction_id": None})
        except Exception as e:
            logger.error(f"Error adding wallet entry: {e}")
            return self.return_message(success=False, message=f"Wallet entry error: {str(e)}", data={"transaction_id": None})

    async def confirm_transaction(self, transaction_id: str) -> WalletServiceReturnMessage:
        """
        Confirm a held wallet transaction.
        Args:
            transaction_id: Transaction ID to confirm
        Returns:
            WalletServiceReturnMessage with confirmation result
        """
        self.url = f"{self.wallet_api_url}/confirm/{transaction_id}"
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(self.url, headers=self._get_headers())
                
                if response.status_code == 200:
                    data = response.json()
                    return self.return_message(success=True, message="Transaction confirmed successfully",
                        data={
                            "transaction_id": transaction_id,
                            "status": data.get("status", "confirmed")
                        }
                    )
                elif response.status_code == 404:
                    return self.return_message(success=False, message="Transaction not found", data={"transaction_id": transaction_id})
                else:
                    logger.error(f"Transaction confirmation failed: {response.status_code} - {response.text}")
                    return self.return_message(success=False, message=f"Confirmation failed: {response.status_code}", data={"transaction_id": transaction_id})
        except httpx.TimeoutException:
            logger.error("Wallet service timeout during confirmation")
            return self.return_message(success=False, message="Wallet service timeout", data={"transaction_id": transaction_id})
        except Exception as e:
            logger.error(f"Error confirming transaction: {e}")
            return self.return_message(success=False, message=f"Confirmation error: {str(e)}", data={"transaction_id": transaction_id})

    async def cancel_transaction(self, transaction_id: str) -> WalletServiceReturnMessage:
        """
        Cancel a held wallet transaction (release the hold).
        Args:
            transaction_id: Transaction ID to cancel
        Returns:
            WalletServiceReturnMessage with cancellation result
        """
        self.url = f"{self.wallet_api_url}/cancel/{transaction_id}"
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(self.url, headers=self._get_headers())
                if response.status_code == 200:
                    data = response.json()
                    return self.return_message(success=True, message="Transaction cancelled successfully",
                        data={
                            "transaction_id": transaction_id,
                            "status": data.get("status", "cancelled")
                        }
                    )
                elif response.status_code == 404:
                    return self.return_message(success=False, message="Transaction not found", data={"transaction_id": transaction_id})
                else:
                    logger.error(f"Transaction cancellation failed: {response.status_code} - {response.text}")
                    return self.return_message(success=False, message=f"Cancellation failed: {response.status_code}", data={"transaction_id": transaction_id})
        except httpx.TimeoutException:
            logger.error("Wallet service timeout during cancellation")
            return self.return_message(success=False, message="Wallet service timeout", data={"transaction_id": transaction_id})
        except Exception as e:
            logger.error(f"Error cancelling transaction: {e}")
            return self.return_message(success=False, message=f"Cancellation error: {str(e)}", data={"transaction_id": transaction_id})
