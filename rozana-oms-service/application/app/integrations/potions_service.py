import httpx
from typing import Dict, Any, Optional
from pydantic import BaseModel
from app.core.constants import OrderStatus

# Redis token cache
from app.connections.redis_wrapper import RedisJSONWrapper

# Request context
from app.middlewares.request_context import request_context

# Logger
from app.logging.utils import get_app_logger
logger = get_app_logger("potions_service")

# Settings
from app.config.settings import OMSConfigs
configs = OMSConfigs()

class PotionsServiceReturnMessage(BaseModel):
    success: bool
    message: str
    task_id: Optional[str] = None
    data: Optional[Dict[str, Any]] = None


class PotionsService:
    """Service for integrating with Potions WMS API"""

    def __init__(self):
        self.potions_config = configs.POTIONS_INTEGRATION_ENABLED
        self.potions_base_url = configs.POTIONS_BASE_URL
        self.potions_client_id = configs.POTIONS_CLIENT_ID
        self.potions_client_secret = configs.POTIONS_CLIENT_SECRET
        self.timeout = configs.POTIONS_TIMEOUT

        # Redis client for token caching
        self.token_cache_db = configs.REDIS_CACHE_DB
        self._redis = RedisJSONWrapper(database=self.token_cache_db)
        self._token_key = "potions:oauth:access_token"

        if not (self.potions_config and self.potions_base_url and self.potions_client_id and self.potions_client_secret):
            raise ValueError("Potions integration is disabled or not configured")

    def return_message(self, success: bool, message: str, task_id: Optional[str] = None, data: Optional[Dict[str, Any]] = None) -> PotionsServiceReturnMessage:
        return PotionsServiceReturnMessage(success=success, message=message, task_id=task_id, data=data)

    async def _get_headers(self) -> Dict[str, str]:
        token = await self._get_oauth_token()
        if not token:
            logger.error("Unable to obtain OAuth token")
            raise ValueError("Unable to obtain OAuth token")
        return {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}"
        }

    async def _get_oauth_token(self) -> str:
        """Return cached token or fetch and cache a new one (minimal flow)."""
        try:
            # Try cache
            if self._redis and self._redis.connected:
                cached = self._redis.get(self._token_key)
                if cached:
                    return cached

            # Fetch new
            token_endpoint = f"{self.potions_base_url}/o/token/"
            token_data = {
                "grant_type": "client_credentials",
                "client_id": self.potions_client_id,
                "client_secret": self.potions_client_secret,
            }
            headers = {'Content-Type': 'application/x-www-form-urlencoded'}
            # Add retry logic
            for _ in range(3):
                async with httpx.AsyncClient(timeout=self.timeout) as client:
                    resp = await client.post(token_endpoint, headers=headers, data=token_data)
                    if resp.status_code == 200:
                        break
            if resp.status_code != 200:
                return None
            data = resp.json()
            token = data.get('access_token')
            exp = data.get('expires_in', 300)
            ttl = max(int(exp) - 60, 60) if isinstance(exp, int) else 300
            if token and self._redis and self._redis.connected:
                try:
                    self._redis.set_with_ttl(self._token_key, token, ttl)
                except Exception:
                    pass
            return token
        except Exception:
            return None

    async def sync_order_by_id(self, facility_name: str, order_id: str, order_service) -> PotionsServiceReturnMessage:
        """Sync order to Potions WMS API by order ID"""
        try:
            request_context.module_name = 'potions_service'
            # Call Potions API to trigger WMS sync
            result = await self._trigger_potions_wms_sync(order_id)
            
            if result.success:
                # Update order status to WMS_SYNCED (21)
                await order_service.update_order_status(order_id, OrderStatus.WMS_SYNCED)
                logger.info(f"potions_wms_sync_success | order_id={order_id} facility_name={facility_name} task_id={result.task_id}")
                return self.return_message(success=True, message="Order synced to Potions WMS successfully", task_id=result.task_id)
            else:
                # Update order status to WMS_SYNC_FAILED (22)
                await order_service.update_order_status(order_id, OrderStatus.WMS_SYNC_FAILED)
                logger.error(f"potions_wms_sync_failed | order_id={order_id} facility_name={facility_name} error={result.message}")
                return self.return_message(success=False, message=f"Failed to sync order to Potions WMS: {result.message}", task_id=result.task_id)

        except Exception as e:
            # Update order status to WMS_SYNC_FAILED (22) on exception
            await order_service.update_order_status(order_id, OrderStatus.WMS_SYNC_FAILED)
            logger.error(f"potions_wms_sync_exception | order_id={order_id} facility_name={facility_name} error={e}", exc_info=True)
            return self.return_message(success=False, message="Exception occurred while syncing to Potions WMS", task_id=None)

    async def _trigger_potions_wms_sync(self, order_id: str) -> PotionsServiceReturnMessage:
        """Trigger Potions WMS sync via API call (simple 2-attempt flow)."""
        try:
            request_context.module_name = 'potions_service'
            payload = {"order_id": order_id}
            endpoint = f"{self.potions_base_url}/api/potions/integrations/order/create/"
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                for attempt in range(2):
                    headers = await self._get_headers()
                    resp = await client.post(endpoint, headers=headers, json=payload)
                    if resp.status_code in (200, 201, 202):
                        data = resp.json() if resp.content else {}
                        return self.return_message(success=True, task_id=data.get('task_id'), message="Order sync triggered")
                    if resp.status_code == 401 and attempt == 0:
                        # Invalidate cached token and retry once
                        try:
                            self._redis.delete(self._token_key)
                        except Exception:
                            pass
                        continue
                    # Non-401 or second failure
                    return self.return_message(success=False, message=f"potions_api_{resp.status_code}", task_id=None)
        except Exception as e:
            logger.error(f"potions_wms_api_exception | order_id={order_id} error={e}", exc_info=True)
            return self.return_message(success=False, message=str(e), task_id=None)


    async def create_reverse_consignment_by_return_reference(self, return_reference: str, order_id: str = None) -> PotionsServiceReturnMessage:
        """Trigger reverse consignment creation in Potions by return reference.

        Prefers OAuth token; falls back to POTIONS_BEARER_TOKEN if OAuth not configured/available.
        Performs a single retry on 401 by invalidating cached OAuth token.
        Handles all errors internally and logs appropriately.
        """
        try:
            request_context.module_name = 'potions_service'
            endpoint = f"{self.potions_base_url}/api/potions/integrations/consignment/create_reverse/return_reference/"
            payload = {"return_reference": return_reference}
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                for attempt in range(2):
                    headers = await self._get_headers()
                    resp = await client.post(endpoint, headers=headers, json=payload)
                    if resp.status_code in (200, 201, 202):
                        data = resp.json() if resp.content else {}
                        logger.info("potions_reverse_consignment_triggered | order_id=%s return_reference=%s status=%s task_id=%s", order_id, return_reference, "reverse_consignment_triggered", data.get('task_id'))
                        return self.return_message(success=True, task_id=data.get('task_id'), message="Reverse consignment triggered in Potions")
                    if resp.status_code == 401 and attempt == 0:
                        # Invalidate cached OAuth token and retry once
                        try:
                            self._redis.delete(self._token_key)
                        except Exception:
                            pass
                        continue
                    # Non-401 or second failure
                    logger.error("potions_reverse_consignment_failed | order_id=%s return_reference=%s status_code=%s error=%s", order_id, return_reference, resp.status_code, resp.text)
                    return self.return_message(success=False, message=f"potions_api_{resp.status_code}", task_id=None)
        except Exception as e:
            logger.error("potions_reverse_consignment_error | order_id=%s return_reference=%s error=%s", order_id, return_reference, str(e), exc_info=True)
            return self.return_message(success=False, message=str(e), task_id=None)

    async def cancel_outbound_order(self, order_reference: str, warehouse: str) -> PotionsServiceReturnMessage:
        """Cancel order in Potions WMS"""
        self.url = f"{self.potions_base_url}/api/potions/integrations/order/cancel/"
        try:
            # Get headers
            headers = await self._get_headers()

            payload = {
                "order_reference": order_reference,
                "facility_id": warehouse
            }

            endpoint = self.url
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                logger.info(f"potions_cancel_api_call | endpoint={endpoint} order_reference={order_reference} warehouse={warehouse} payload={payload}")
                
                response = await client.post(endpoint, headers=headers, json=payload)
                if response.status_code in [200, 201, 202]:
                    response_data = response.json() if response.content else {}
                    logger.info(f"potions_cancel_api_success | order_reference={order_reference} response={response_data}")
                    return self.return_message(success=True, task_id=response_data.get('task_id'), message="Order cancellation triggered successfully in Potions WMS")
                else:
                    error_msg = response.text
                    logger.error(f"potions_cancel_api_error | order_reference={order_reference} status_code={response.status_code} error={error_msg}")
                    return self.return_message(success=False, message=f"potions_api_{response.status_code}", task_id=None)
        except Exception as e:
            logger.error(f"potions_cancel_api_exception | order_reference={order_reference} error={e}", exc_info=True)
            return self.return_message(success=False, message=str(e), task_id=None)

    async def process_return(self, return_data: Dict[str, Any]) -> PotionsServiceReturnMessage:
        """Process return in Potions WMS (placeholder for future implementation)"""
        logger.info(f"potions_return_not_implemented | return_data_keys={list((return_data or {}).keys())}")
        return self.return_message(success=True, message="Return processing not implemented in Potions WMS - handled in OMS only", task_id=None)
