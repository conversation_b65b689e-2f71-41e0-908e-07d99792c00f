"""
Wallet payment service for handling wallet-based payments.
"""

import logging
from typing import Dict, Any
from decimal import Decimal

from app.services.payment_service import payment_service
from app.core.constants import PaymentStatus

# Integrations
from app.integrations.wallet_service import WalletService

# Logger
from app.logging.utils import get_app_logger
logger = get_app_logger("wallet_payment_service")

class WalletPaymentService:
    """Service for handling wallet payments"""
    
    async def process_wallet_payment(
        self,
        customer_id: str,
        order_id: int,
        amount: Decimal,
        payment_id: str,
        description: str = None
    ) -> Dict[str, Any]:
        """
        Process a wallet payment by debiting the wallet and creating payment record.
        
        Args:
            customer_id: Customer ID
            order_id: Order ID
            amount: Payment amount
            description: Payment description
            
        Returns:
            Dict with payment processing result
        """
        try:
            # Add wallet entry (debit)
            debit_result = await WalletService().add_wallet_entry(
                customer_id=customer_id,
                amount=amount,
                order_id=str(order_id),
                payment_id=payment_id,
                entry_type="debit",
                reference_type="order_payment",
                description=description or f"Payment for order {order_id}"
            )

            if not debit_result.success:
                # On any failure (e.g., insufficient balance, wrong OTP), mark payment as FAILED
                await payment_service.update_payment_status(
                    payment_id=payment_id,
                    new_status=PaymentStatus.FAILED
                )
                # Re-run order completion check (will keep order pending/incomplete)
                await self.confirm_wallet_payments_and_update_order(order_id)
                return {
                    "success": False,
                    "message": debit_result.message,
                    "payment_id": payment_id,
                    "transaction_id": None,
                    "status": "failed"
                }
            
            # Success path: use the correct key exposed by WalletService().add_wallet_entry
            transaction_id = debit_result.data.get("transaction_id", None)

            # Update payment status to completed
            update_result = await payment_service.update_payment_status(
                payment_id=payment_id,
                new_status=PaymentStatus.COMPLETED
            )

            await self.confirm_wallet_payments_and_update_order(order_id)   

            if not update_result.get("success", False):
                logger.error(f"Failed to update payment status for {payment_id}: {update_result.get('message')}")
                # Wallet already debited, so we don't fail the entire operation
                
            logger.info(f"Wallet payment processed successfully: {payment_id}")

            return {
                "success": True,
                "message": "Wallet payment processed successfully",
                "payment_id": payment_id,
                "transaction_id": transaction_id,
                "status": "completed"
            }
            
        except Exception as e:
            logger.error(f"Error processing wallet payment: {e}")
            # Best-effort mark as FAILED on unexpected errors
            try:
                await payment_service.update_payment_status(
                    payment_id=payment_id,
                    new_status=PaymentStatus.FAILED
                )
            except Exception:
                pass
            return {
                "success": False,
                "message": f"Wallet payment error: {str(e)}",
                "payment_id": payment_id,
                "transaction_id": None,
                "status": "failed"
            }
    
    async def confirm_wallet_payment(self, payment_id: str, order_id: int) -> Dict[str, Any]:
        """
        Confirm a wallet payment by confirming the wallet transaction.
        
        Args:
            payment_id: Payment ID (wallet_<transaction_id>)
            
        Returns:
            Dict with confirmation result
        """
        try:
            # Update payment status to completed
            update_result = await payment_service.update_payment_status(
                payment_id=payment_id,
                new_status=PaymentStatus.COMPLETED
            )

            if not update_result.get("success", False):
                logger.error(f"Failed to update payment status for {payment_id}: {update_result.get("message", "")}")
                # Note: Wallet transaction is already confirmed, so we log but don't fail
            
            logger.info(f"Wallet payment confirmed successfully: {payment_id}")
            
            return {
                "success": True,
                "message": "Wallet payment confirmed successfully",
                "payment_id": payment_id,
                "transaction_id": None,
                "status": "confirmed"
            }
            
        except Exception as e:
            logger.error(f"Error confirming wallet payment: {e}")
            return {
                "success": False,
                "message": f"Wallet confirmation error: {str(e)}"
            }
    
    async def cancel_wallet_payment(self, payment_id: str) -> Dict[str, Any]:
        """
        Cancel a wallet payment by cancelling the wallet transaction.
        
        Args:
            payment_id: Payment ID (wallet_<transaction_id>)
            
        Returns:
            Dict with cancellation result
        """
        try:
            # Extract transaction ID from payment ID
            if not payment_id.startswith("wallet_"):
                return {
                    "success": False,
                    "message": "Invalid wallet payment ID format"
                }
            
            transaction_id = payment_id.replace("wallet_", "")
            
            # Cancel transaction with wallet service
            cancel_result = await WalletService().cancel_transaction(transaction_id)
            
            if not cancel_result.success:
                return {
                    "success": False,
                    "message": cancel_result.message
                }
            
            # Update payment status to failed
            update_result = await payment_service.update_payment_status(
                payment_id=payment_id,
                new_status=PaymentStatus.FAILED
            )

            if not update_result.get("success", False):
                logger.error(f"Failed to update payment status for {payment_id}: {update_result.get("success", False)}")

            logger.info(f"Wallet payment cancelled successfully: {payment_id}")

            return {
                "success": True,
                "message": "Wallet payment cancelled successfully",
                "payment_id": payment_id,
                "transaction_id": transaction_id,
                "status": "cancelled"
            }
            
        except Exception as e:
            logger.error(f"Error cancelling wallet payment: {e}")
            return {
                "success": False,
                "message": f"Wallet cancellation error: {str(e)}"
            }
    
    async def confirm_wallet_payments_and_update_order(self, order_id: int, background_tasks=None) -> Dict[str, Any]:
        """
        Background job to confirm wallet payments and check order completion.
        
        Args:
            order_id: Order ID to process
            
        Returns:
            Dict with job result
        """
        try:
            # Use unified payment completion check
            completion_result = await payment_service.check_and_complete_order_if_all_payments_successful(order_id)
            
            return {
                "success": True,
                "message": f"Wallet payments processed for order {order_id}",
                "completion_check": completion_result
            }

        except Exception as e:
            logger.error(f"Error in wallet confirmation job for order {order_id}: {e}")
            return {
                "success": False,
                "message": f"Background job error: {str(e)}"
            }


# Global wallet payment service instance
wallet_payment_service = WalletPaymentService()
