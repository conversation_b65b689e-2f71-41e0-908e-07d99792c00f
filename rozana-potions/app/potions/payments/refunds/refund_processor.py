from datetime import datetime, timezone
from decimal import Decimal
from typing import Optional, Dict, Any
from pydantic import BaseModel
from payments.constants import RefundStatus, PaymentStatus
from payments.services.razorpay_service import RazorpayService
from payments.services.wallet_service import WalletService

# Repositories
from core.repository.payments import PaymentRepository
from core.repository.refunds_details import RefundRepository
from core.repository.orders import OrderRepository

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('refund_processor')

class RefundProcessorReturnMessage(BaseModel):
    success: bool
    message: str
    data: Optional[Dict] = None

class RefundProcessor:
    def __init__(self):
        self.razorpay_service = RazorpayService()
        self.wallet_service = WalletService()
        self.payment_repository = PaymentRepository()
        self.refund_repository = RefundRepository()
        self.order_repository = OrderRepository()

    def create_refund_by_order_id(self, order_id: str) -> RefundProcessorReturnMessage:
        """Create refunds for all payments of an order (supports razorpay, wallet, cash)"""
        try:
            # Get all payments for the order
            all_payments = self.payment_repository.get_payment_details_by_order_id(order_id)
            logger.info(f"Found {len(all_payments)} payments for order {order_id}")

            if not all_payments:
                return RefundProcessorReturnMessage(success=False, message=f"No payments found for order {order_id}")

            # Filter only completed payments
            completed_payments = []
            for payment in all_payments:
                if payment.get('payment_status') == PaymentStatus.COMPLETED:
                    completed_payments.append(payment)

            if not completed_payments:
                return RefundProcessorReturnMessage(success=False, message=f"No completed payments found for order {order_id}")
            
            logger.info(f"Processing refunds for {len(completed_payments)} completed payments")
            
            total_refund_amount = Decimal('0')
            all_successful = True
            # Get customer id from orders table
            customer_id = self.order_repository.get_customer_id_by_order_id(order_id)
            if not customer_id:
                return RefundProcessorReturnMessage(success=False, message=f"No customer found for order {order_id}")
            
            for payment in completed_payments:
                payment_mode = payment.get('payment_mode', '').lower()
                payment_amount = Decimal(str(payment.get('payment_amount', 0)))
                
                logger.info(f"Processing refund for payment {payment.get('payment_id')} - Mode: {payment_mode}, Amount: {payment_amount}")
                
                if payment_mode == 'razorpay':
                    refund_result = self._process_razorpay_refund(payment, order_id)
                elif payment_mode in ['cash', 'wallet']:
                    refund_result = self._process_wallet_refund(payment, order_id, customer_id)
                else:
                    logger.warning(f"Unknown payment mode: {payment_mode} for payment {payment.get('payment_id')}")
                    data = {
                        "payment_id": payment.get('payment_id'),
                        "payment_mode": payment_mode
                    }
                    refund_result = RefundProcessorReturnMessage(success=False,
                        message=f"Unsupported payment mode: {payment_mode}",
                        data=data
                    )

                if refund_result.success:
                    total_refund_amount += Decimal(str(refund_result.data.get("refund_amount", 0)))
                else:
                    all_successful = False
                    logger.error(f"Refund failed for payment {payment.get('payment_id')}: {refund_result.message}")
            
            final_result = {
                "success": all_successful,
                "order_id": order_id,
                "total_refund_amount": float(total_refund_amount),
                "message": "All refunds processed successfully" if all_successful else "Some refunds failed",
                "processed_at": datetime.now(timezone.utc).isoformat()
            }
            return RefundProcessorReturnMessage(success=all_successful, message=final_result["message"], data=final_result)

        except Exception as e:
            logger.error(f"Multi-payment refund error | order_id={order_id} error={e}", exc_info=True)
            return RefundProcessorReturnMessage(success=False, message=f"Error processing refunds for order {order_id}: {str(e)}")

    def _process_razorpay_refund(self, payment: Dict[str, Any], order_id: str) -> RefundProcessorReturnMessage:
        """Process Razorpay refund"""
        try:
            payment_id = payment.get('payment_id')
            payment_amount = float(payment.get('payment_amount'))

            # Trigger refund in Razorpay
            razorpay_response = self.razorpay_service.create_refund(payment_id, {"order_reference": order_id}, amount=payment_amount)
            logger.info(f"Razorpay refund response for payment {payment_id}: {razorpay_response}")

            if not razorpay_response.get("success"):
                return RefundProcessorReturnMessage(
                    success=False,
                    message=razorpay_response.get("message", "Failed to create Razorpay refund"),
                    data={
                        "payment_id": payment_id,
                        "payment_mode": "razorpay",
                        "error": razorpay_response.get("error", "Unknown error")
                    }
                )

            refund = razorpay_response.get("refund", {})
            refund_id = refund.get("id")
            razorpay_status = (refund.get('status') or '').lower()

            # Create refund record in database
            self.refund_repository.create_refund_record(payment.get('id'), refund_id, payment_amount)
            
            # Update statuses if processed
            if razorpay_status == 'processed':
                self.refund_repository.update_refund_status(refund_id, RefundStatus.PROCESSED)
                self.payment_repository.update_payment_details_status(payment_id, PaymentStatus.REFUNDED)

            logger.info(f"Created Razorpay refund: {refund_id} for amount: {payment_amount}")

            refund_status = RefundStatus.CREATED
            if razorpay_status == 'processed':
                refund_status = RefundStatus.PROCESSED

            result = {
                "refund_id": refund_id,
                "refund_amount": float(payment_amount),
                "refund_status": RefundStatus.get_description(refund_status),
                "payment_id": payment_id,
                "payment_mode": "razorpay",
                "refund_method": "razorpay_api"
            }

            return RefundProcessorReturnMessage(success=True, message="Razorpay refund processed successfully", data=result)

        except Exception as e:
            logger.error(f"Razorpay refund error for payment {payment.get('payment_id')}: {e}")
            return RefundProcessorReturnMessage(
                success=False, 
                message=f"Error processing Razorpay refund: {str(e)}", 
                data={"payment_id": payment.get('payment_id'), "payment_mode": "razorpay"}
            )

    def _process_wallet_refund(self, payment: Dict[str, Any], order_id: str, customer_id: str) -> RefundProcessorReturnMessage:
        """Process refund to wallet for cash/wallet payments"""
        try:
            if not self.wallet_service:
                return RefundProcessorReturnMessage(
                    success=False,
                    message="Wallet service not available",
                    data={"payment_id": payment.get('payment_id'), "payment_mode": payment.get('payment_mode')}
                )

            payment_id = payment.get('payment_id')
            payment_amount = Decimal(str(payment.get('payment_amount')))
            payment_mode = payment.get('payment_mode')

            # Add wallet entry as credit (refund)
            wallet_result = self.wallet_service.add_wallet_entry(
                customer_id=str(customer_id),
                amount=payment_amount,
                order_id=order_id,
                payment_id=payment_id,
                entry_type="credit",
                reference_type="order_refund",
                description=f"Refund for {payment_mode} payment - Order {order_id}"
            )

            if not wallet_result.success:
                return RefundProcessorReturnMessage(
                    success=False,
                    message=wallet_result.message,
                    data={"payment_id": payment_id, "payment_mode": payment_mode}
                )

            # Generate refund ID for wallet refunds
            transaction_id = wallet_result.data.get('transaction_id')
            wallet_refund_id = f"wallet_refund_{transaction_id if transaction_id else payment_id}"

            # Create refund record in database
            self.refund_repository.create_refund_record(payment.get('id'), wallet_refund_id, payment_amount)

            # Update statuses immediately for wallet refunds
            self.refund_repository.update_refund_status(wallet_refund_id, RefundStatus.PROCESSED)
            self.payment_repository.update_payment_details_status(payment_id, PaymentStatus.REFUNDED)

            logger.info(f"Created wallet refund: {wallet_refund_id} for amount: {payment_amount} to customer: {customer_id}")

            result = {
                "refund_id": wallet_refund_id,
                "refund_amount": float(payment_amount),
                "refund_status": RefundStatus.get_description(RefundStatus.PROCESSED),
                "payment_id": payment_id,
                "payment_mode": payment_mode,
                "refund_method": "wallet_credit",
                "customer_id": customer_id,
                "wallet_transaction_id": wallet_result.data.get('transaction_id'),
                "wallet_balance_after": wallet_result.data.get('balance_after')
            }
            return RefundProcessorReturnMessage(success=True, message="Wallet refund processed successfully", data=result)

        except Exception as e:
            logger.error(f"Wallet refund error for payment {payment.get('payment_id')}: {e}")
            return RefundProcessorReturnMessage(
                success=False,
                message=f"Error processing wallet refund: {str(e)}",
                data={"payment_id": payment.get('payment_id'), "payment_mode": payment.get('payment_mode')}
            )
