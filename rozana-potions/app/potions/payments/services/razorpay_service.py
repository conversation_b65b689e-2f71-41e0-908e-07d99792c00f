import hmac
import hashlib
import razorpay
from typing import Dict, Any
from django.conf import settings
from potions.logging.utils import get_app_logger

logger = get_app_logger('razorpay_service')

# Constants
RAZORPAY_WEBHOOK_SECRET = getattr(settings, 'RAZORPAY_WEBHOOK_SECRET', None)
RAZORPAY_INTEGRATION_ENABLED = getattr(settings, 'RAZORPAY_INTEGRATION_ENABLED', False)
RAZORPAY_KEY_ID = getattr(settings, 'RAZORPAY_KEY_ID', None)
RAZORPAY_KEY_SECRET = getattr(settings, 'RAZORPAY_KEY_SECRET', None)


class RazorpayService:
    def __init__(self):
        self.integration_enabled = RAZORPAY_INTEGRATION_ENABLED
        if not self.integration_enabled:
            logger.warning("Razorpay integration disabled")
            self.client = None
        else:
            self.client = razorpay.Client(auth=(RAZOR<PERSON>Y_KEY_ID, RA<PERSON>OR<PERSON>Y_KEY_SECRET))

    @staticmethod
    def _verify_signature(payload: bytes, signature: str, secret: str) -> bool:
        try:
            expected_signature = hmac.new(secret.encode(), payload, hashlib.sha256).hexdigest()
            return hmac.compare_digest(expected_signature, signature)
        except Exception as e:
            logger.error(f"Signature verification error: {e}")
            return False

    def verify_webhook_signature(self, payload_bytes: bytes, signature: str) -> bool:
        if not all([payload_bytes, signature, RAZORPAY_WEBHOOK_SECRET]):
            return False
        return self._verify_signature(payload_bytes, signature, RAZORPAY_WEBHOOK_SECRET)

    def create_refund(self, payment_id: str, notes: Dict[str, Any] = None, amount: int = None) -> Dict[str, Any]:
        if not self.client:
            return {"success": False, "message": "Razorpay integration is disabled"}

        try:
            extra_data = {"notes": notes or {}}
            if amount:
                extra_data["amount"] = amount * 100 # convert to paise
            refund = self.client.payment.refund(payment_id, extra_data)
            return {"success": True, "refund": refund}
            
        except Exception as e:
            error_str = str(e)
            
            if "fully refunded already" in error_str.lower():
                message = "Payment has already been fully refunded"
            elif "invalid payment id" in error_str.lower():
                message = "Invalid payment ID provided"
            elif "payment not captured" in error_str.lower():
                message = "Payment must be captured before refund"
            elif "insufficient balance" in error_str.lower():
                message = "Insufficient balance in Razorpay account"
            else:
                message = f"Razorpay API error: {error_str}"

            return {"success": False, "error": error_str, "message": message}

