from typing import Dict, Optional

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('order_repository')

# Database wrapper
from core.repository.main import OMSDatabase

class OrderRepository:
    """Repository for order operations on OMS database"""

    def __init__(self):
        self.db = OMSDatabase()

    def get_order_by_id(self, order_id: str) -> Optional[Dict]:
        """Get order by external order_id"""
        query = """
            SELECT * FROM orders 
            WHERE order_id = %s
        """
        return self.db.fetch_one(query, (order_id,))

    def update_order_status(self, order_id: str, new_status: int) -> bool:
        """Update order status"""
        query = """
            UPDATE orders 
            SET status = %(new_status)s, updated_at = NOW()
            WHERE order_id = %(order_id)s
        """
        affected_rows = self.db.execute_update(query, {
            'order_id': order_id,
            'new_status': new_status
        })
        return affected_rows > 0

    def get_customer_id_by_order_id(self, order_id: str) -> Optional[str]:
        """Get customer id by order_id"""
        query = """
            SELECT customer_id FROM orders
            WHERE order_id = %s
        """
        result = self.db.fetch_one(query, (order_id,))
        return result.get('customer_id') if result else None

order_repository = OrderRepository()
