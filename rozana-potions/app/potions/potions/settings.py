"""
Django settings for potions project.

Generated by 'django-admin startproject' using Django 5.2.5.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-ne0k^=o!ae(&e09gyfoxo$sni)km-a+^_fa%@pkj@dhf#q@@-e'

ALLOWED_HOSTS = ['*']


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'channels',
    'storages',
    'rest_framework',
    'oauth2_provider',
    'corsheaders',
    'django_json_widget',
    'django_celery_beat',
    'events',
    'core',
    'scripts',
    'integrations',
    'payments',
]

MIDDLEWARE = [
    'oauth2_provider.middleware.OAuth2TokenMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'potions.middlewares.logging_middleware.AuditMiddleware',
    'potions.middlewares.auth_middleware.OAuth2IntegrationMiddleware',
    'potions.middlewares.webhook_middleware.WebhookAuthenticationMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'potions.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'potions.wsgi.application'
ASGI_APPLICATION = 'potions.asgi.application'


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

import os
from dotenv import load_dotenv

load_dotenv()

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.getenv('DEBUG', '0') == '1'

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.getenv('DATABASE_NAME', 'potions'),
        'USER': os.getenv('DATABASE_USER', 'postgres'),
        'PASSWORD': os.getenv('DATABASE_PASSWORD', 'rozana^1234'),
        'HOST': os.getenv('DATABASE_HOST', 'db1'),
        'PORT': os.getenv('DATABASE_PORT', '5432'),
    },
    'oms': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.getenv('OMS_DATABASE_NAME', 'oms'),
        'USER': os.getenv('OMS_DATABASE_USER', 'postgres'),
        'PASSWORD': os.getenv('OMS_DATABASE_PASSWORD', 'rozana^1234'),
        'HOST': os.getenv('OMS_DATABASE_HOST', 'db1'),
        'PORT': os.getenv('OMS_DATABASE_PORT', '5432'),
    }
}

# Razorpay Configuration (Same as OMS)
RAZORPAY_INTEGRATION_ENABLED = os.getenv("RAZORPAY_INTEGRATION_ENABLED", "false").lower() == "true"
RAZORPAY_KEY_ID = os.getenv("RAZORPAY_KEY_ID")
RAZORPAY_KEY_SECRET = os.getenv("RAZORPAY_KEY_SECRET")
RAZORPAY_WEBHOOK_SECRET = os.getenv("RAZORPAY_WEBHOOK_SECRET")
RAZORPAY_BASE_URL = os.getenv("RAZORPAY_BASE_URL", "https://api.razorpay.com/v1")
RAZORPAY_CURRENCY = os.getenv("RAZORPAY_CURRENCY", "INR")
RAZORPAY_TIMEOUT = int(os.getenv("RAZORPAY_TIMEOUT", "30"))

# Wallet Configuration
WALLET_INTEGRATION_ENABLED = os.getenv("WALLET_INTEGRATION_ENABLED", "false").lower() == "true"
WALLET_BASE_URL = os.getenv("WALLET_BASE_URL")
WALLET_INTERNAL_API_KEY = os.getenv("WALLET_INTERNAL_API_KEY")

# TMS Configuration
TMS_BASE_URL = os.getenv('TMS_BASE_URL', 'https://app.shipsy.in')
TMS_API_KEY = os.getenv('TMS_API_KEY')
TMS_WEBHOOK_TOKEN = os.getenv('TMS_WEBHOOK_TOKEN', 'static_webhook_token_123')

# Invoice Webhook Configuration
INVOICE_WEBHOOK_TOKEN = os.getenv('INVOICE_WEBHOOK_TOKEN', 'static_webhook_token_123')

# Environment Configuration
APPLICATION_ENVIRONMENT = os.getenv('APPLICATION_ENVIRONMENT', 'UAT')  # UAT or PRODUCTION
APPLICATION_PROTOCOL = os.getenv('APPLICATION_PROTOCOL', 'http')

# Liink API Configuration
LIINK_BASE_URL = os.getenv('LIINK_BASE_URL', 'https://liink-backend.stockone.com')
LIINK_AUTH_TOKEN = os.getenv('LIINK_AUTH_TOKEN', '')
LIINK_WORKSPACE = os.getenv('LIINK_WORKSPACE', 'ROZANA')
LIINK_CONNECTOR_URL = os.getenv('LIINK_CONNECTOR_URL', '/api/v1/base/connectors/WMS_NEO/SOCreationWithPack/161')

# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

# AWS S3 Static Files Configuration
USE_S3 = os.getenv('USE_S3', '0') == '1'

STATIC_URL = '/static/'

if USE_S3:
    print("Using S3 for static files")
    # S3 Static Files Storage
    AWS_ACCESS_KEY_ID = os.getenv('AWS_ACCESS_KEY_ID', '')
    AWS_SECRET_ACCESS_KEY = os.getenv('AWS_SECRET_ACCESS_KEY', '')
    AWS_STORAGE_BUCKET_NAME = os.getenv('AWS_STORAGE_BUCKET_NAME', 'rozana-potions')

    # S3 Storage settings
    AWS_S3_OBJECT_PARAMETERS = {'CacheControl': 'max-age=86400'}  # 1 day cache
    AWS_S3_FILE_OVERWRITE = False
    AWS_S3_REGION_NAME = os.getenv('AWS_S3_REGION_NAME', 'ap-south-1')
    AWS_S3_CUSTOM_DOMAIN = f'{AWS_STORAGE_BUCKET_NAME}.s3.{AWS_S3_REGION_NAME}.amazonaws.com'
    AWS_S3_SIGNATURE_VERSION = os.environ.get('AWS_S3_SIGNATURE_VERSION','s3v4')
    AWS_DEFAULT_ACL = 'public-read'
    AWS_S3_VERIFY = True
    AWS_LOCATION = 'static'

    # s3 static settings
    STATIC_LOCATION = 'static'
    STATIC_URL = f'https://{AWS_S3_CUSTOM_DOMAIN}/{STATIC_LOCATION}/'

    # Django 5.x STORAGES configuration
    STORAGES = {
        "default": {
            "BACKEND": "potions.storage.PublicMediaStorage",
        },
        "staticfiles": {
            "BACKEND": "potions.storage.AWSStaticStorage",
        },
    }

else:
    # Local static files configuration
    STATIC_URL = '/static/'
    STATIC_ROOT = os.path.join(BASE_DIR, 'static')

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Celery Configuration
CELERY_BROKER_URL = os.environ.get('REDIS_BROKER_URL', 'redis://redis:6379')
CELERY_RESULT_BACKEND = os.environ.get('REDIS_BROKER_URL', 'redis://redis:6379')  + "/1"
CELERY_RESULT_EXPIRES = os.environ.get('CELERY_RESULT_EXPIRES', 3600)
CELERY_CACHE_BACKEND = 'default'
REDIS_BROKER_URL = os.environ.get('REDIS_BROKER_URL', 'redis://redis:6379')

# Django Channels Configuration
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels_redis.core.RedisChannelLayer',
        'CONFIG': {
            "hosts": [REDIS_BROKER_URL + "/2"],  # Use Redis DB 2 for channels
        },
    },
}

# Celery Beat Configuration - Explicit task registration for Django admin
CELERY_BEAT_SCHEDULER = 'django_celery_beat.schedulers:DatabaseScheduler'
CELERY_IMPORTS = [
    'scripts.tasks.inventory',
    'integrations.tasks.invoice_pdf',
    'events.tasks',
    'potions.celery',
]

# Task discovery settings
CELERY_TASK_ALWAYS_EAGER = False
CELERY_TASK_EAGER_PROPAGATES = True


CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': REDIS_BROKER_URL,
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# Django REST Framework Configuration
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'oauth2_provider.contrib.rest_framework.OAuth2Authentication',
        'rest_framework.authentication.SessionAuthentication',
    ),
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.AllowAny',
    ],
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20
}

# Django OAuth Toolkit Configuration
OAUTH2_PROVIDER = {
    'ACCESS_TOKEN_EXPIRE_SECONDS': 3600,
    'REFRESH_TOKEN_EXPIRE_SECONDS': 3600 * 24 * 7,  # 7 days
    'AUTHORIZATION_CODE_EXPIRE_SECONDS': 600,
    'ROTATE_REFRESH_TOKEN': True,
}

# CORS Configuration (for frontend integration)
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://localhost:8080",
    "http://127.0.0.1:8080",
]

CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_ALL_ORIGINS = DEBUG  # Only allow all origins in debug mode

CSRF_TRUSTED_ORIGINS = ['https://*.rozana.in', 'https://*.rozana.tech', 'http://localhost:8004', 'https://*.app.github.dev']


# Core logging settings
ASYNC_LOGGING = os.getenv('ASYNC_LOGGING', 'TRUE') == 'TRUE'
BATCH_PROCESSING = os.getenv('BATCH_PROCESSING', 'TRUE') == 'TRUE'

# Buffer settings
APP_LOGS_CAPACITY = int(os.getenv('APP_LOGS_CAPACITY', 100))
AUDIT_LOGS_CAPACITY = int(os.getenv('AUDIT_LOGS_CAPACITY', 50))
LOG_BUFFER_TIMEOUT = int(os.getenv('LOG_BUFFER_TIMEOUT', 30))

# Process management
LOG_PROCESSOR_POOL_SIZE = int(os.getenv('LOG_PROCESSOR_POOL_SIZE', 3))
MAX_QUEUE_SIZE = int(os.getenv('MAX_QUEUE_SIZE', 1000))

# Firehose settings (required)
FIREHOSE_REGION_NAME = os.getenv('FIREHOSE_REGION_NAME', 'ap-south-1')
FIREHOSE_ACCESS_KEY_ID = os.getenv('FIREHOSE_ACCESS_KEY_ID', '')
FIREHOSE_SECRET_ACCESS_KEY = os.getenv('FIREHOSE_SECRET_ACCESS_KEY', '')
FIREHOSE_RETRY_COUNT = int(os.getenv('FIREHOSE_RETRY_COUNT', 3))
FIREHOSE_RETRY_DELAY = int(os.getenv('FIREHOSE_RETRY_DELAY', 1))

# Initialize logging system
try:
    from potions.logging.utils import initialize_logging
    initialize_logging()
except Exception as e:
    print(f"Failed to initialize logging: {e}")

# Standard Django Logging Configuration (fallback)
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
        'file': {
            'class': 'logging.FileHandler',
            'filename': 'potions.log',
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
        'potions': {
            'handlers': ['console', 'file'],
            'level': 'DEBUG' if DEBUG else 'INFO',
            'propagate': False,
        },
    },
}

# WhatsApp Configuration
WHATSAPP_ENABLED = os.getenv('WHATSAPP_ENABLED', 'false').lower() == 'true'
GUPSHUP_URL = os.getenv('GUPSHUP_URL')
GUPSHUP_USERID = os.getenv('GUPSHUP_USERID')
GUPSHUP_PASSWORD = os.getenv('GUPSHUP_PASSWORD')
GUPSHUP_TIMEOUT = float(os.getenv('GUPSHUP_TIMEOUT', '30.0'))
