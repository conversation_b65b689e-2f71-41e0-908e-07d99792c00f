from django.conf import settings
from potions.logging.utils import get_app_logger
from integrations.tasks.whatsapp import send_whatsapp_event_notification_task

logger = get_app_logger("whatsapp_notifications")

whatsapp_status = getattr(settings, 'WHATSAPP_ENABLED', False)

class WhatsappNotification:
    @staticmethod
    def send_whatsapp_event_notification(event_type: str, payload: dict, metadata: dict = {}):
        logger.info(f"whatsapp status flag: {whatsapp_status}")
        if whatsapp_status:
            try:
                send_whatsapp_event_notification_task(event_type=event_type,payload=payload,metadata=metadata)
                logger.info(f"WhatsApp notification queued for order {payload.get('order_id', 'unknown')}")
            except Exception as e:
                logger.error(f"Failed to queue WhatsApp notification: {str(e)}")
        else:
            logger.info("WhatsApp notifications are disabled")
        
        return {"ok": True}
